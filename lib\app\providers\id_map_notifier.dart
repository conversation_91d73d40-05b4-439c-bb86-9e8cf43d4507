// id_map_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';

abstract class IdMapNotifier extends Notifier<Map<String, int>> {
  @override
  Map<String, int> build() => {};

  void _updateState(Map<String, int> newState) {
    state = newState;
  }

  bool hasItem(String slug) => state.containsKey(slug);

  int? getId(String slug) => state[slug];

  List<String> get slugs => state.keys.toList();

  List<int> get ids => state.values.toList();

  void addItem(String slug, int id) {
    if (state.containsKey(slug)) return;
    final updated = Map<String, int>.from(state);
    updated[slug] = id;
    _updateState(updated);
  }

  void addItems(Map<String, int> newItems) {
    if (newItems.isEmpty) return;
    final filtered = Map<String, int>.fromEntries(
      newItems.entries.where((entry) => !state.containsKey(entry.key)),
    );
    if (filtered.isEmpty) return;
    final updated = Map<String, int>.from(state);
    updated.addAll(filtered);
    _updateState(updated);
  }

  void updateItem(String slug, int id) {
    if (!state.containsKey(slug)) return;
    final updated = Map<String, int>.from(state);
    updated[slug] = id;
    _updateState(updated);
  }

  void upsertItem(String slug, int id) {
    final updated = Map<String, int>.from(state);
    updated[slug] = id;
    _updateState(updated);
  }

  void removeItem(String slug) {
    if (!state.containsKey(slug)) return;
    final updated = Map<String, int>.from(state);
    updated.remove(slug);
    _updateState(updated);
  }

  void removeItems(List<String> slugs) {
    if (slugs.isEmpty) return;
    final updated = Map<String, int>.from(state);
    for (final slug in slugs) {
      updated.remove(slug);
    }
    _updateState(updated);
  }

  void replaceAll(Map<String, int> newMap) {
    _updateState(newMap);
  }

  void clear() {
    _updateState({});
  }

  // Getter methods for better performance.

  int get length => state.length;

  bool get isEmpty => state.isEmpty;

  bool get isNotEmpty => state.isNotEmpty;
}
